#!/usr/bin/env node

/**
 * 测试官方知识库集成功能
 */

import axios from 'axios';

const AXMOL_SEARCH_BASE = "https://axmol.dev/manual/latest/search";
const AXMOL_OFFICIAL_DOCS = "https://axmol.dev/manual/latest";

/**
 * 解析搜索数据JavaScript文件
 */
function parseSearchData(jsContent) {
  try {
    // 提取 searchData 数组
    const match = jsContent.match(/var\s+searchData\s*=\s*(\[[\s\S]*?\]);/);
    if (match) {
      const dataString = match[1];
      // 使用 eval 解析 JavaScript 数组（在受控环境中是安全的）
      const searchData = eval(dataString);
      return searchData;
    }
    return [];
  } catch (error) {
    console.log('⚠️ 解析搜索数据失败:', error.message);
    return [];
  }
}

/**
 * 过滤相关的搜索结果
 */
function filterRelevantResults(searchData, searchTerms, categories) {
  const results = [];
  
  searchData.forEach((item) => {
    try {
      const [id, data] = item;
      const [name, urlData] = data;
      
      // 计算相关性分数
      let relevanceScore = 0;
      const nameLower = name.toLowerCase();
      const idLower = id.toLowerCase();
      
      // 检查搜索词匹配
      searchTerms.forEach(term => {
        const termLower = term.toLowerCase();
        if (nameLower.includes(termLower)) {
          relevanceScore += 10; // 名称匹配权重最高
        }
        if (idLower.includes(termLower)) {
          relevanceScore += 5; // ID匹配权重中等
        }
      });
      
      // 检查类别匹配
      categories.forEach(category => {
        if (nameLower.includes(category) || idLower.includes(category)) {
          relevanceScore += 3;
        }
      });
      
      // 只保留有相关性的结果
      if (relevanceScore > 0) {
        // 处理URL数据
        let urls = [];
        if (Array.isArray(urlData)) {
          if (Array.isArray(urlData[0])) {
            // 多个结果的情况
            urls = urlData.map(url => ({
              path: url[0],
              type: url[1],
              scope: url[2] || ''
            }));
          } else {
            // 单个结果的情况
            urls = [{
              path: urlData[0],
              type: urlData[1],
              scope: urlData[2] || ''
            }];
          }
        }
        
        results.push({
          type: 'official_docs',
          id,
          name,
          urls,
          relevanceScore,
          matchedTerms: searchTerms.filter(term => 
            nameLower.includes(term.toLowerCase()) || idLower.includes(term.toLowerCase())
          )
        });
      }
    } catch (error) {
      console.log('⚠️ 处理搜索项失败:', error.message);
    }
  });
  
  return results;
}

/**
 * 测试官方知识库搜索
 */
async function testOfficialDocsSearch() {
  console.log('🧪 开始测试官方知识库搜索功能...\n');
  
  const testCases = [
    {
      name: "Sprite 相关搜索",
      searchTerms: ["sprite", "create"],
      categories: ["sprite"]
    },
    {
      name: "Node 相关搜索", 
      searchTerms: ["node", "addchild"],
      categories: ["scene"]
    },
    {
      name: "Animation 相关搜索",
      searchTerms: ["animation", "action"],
      categories: ["animation"]
    }
  ];
  
  for (const testCase of testCases) {
    console.log(`\n📋 测试案例: ${testCase.name}`);
    console.log(`搜索词: ${testCase.searchTerms.join(', ')}`);
    console.log(`类别: ${testCase.categories.join(', ')}`);
    
    try {
      // 获取搜索字符
      const searchChars = new Set();
      testCase.searchTerms.forEach(term => {
        if (term.length > 0) {
          const firstChar = term.toLowerCase().charAt(0);
          searchChars.add(firstChar);
        }
      });
      
      console.log(`需要获取的搜索文件: ${Array.from(searchChars).map(c => `all_${c}.js`).join(', ')}`);
      
      // 获取搜索数据
      const results = [];
      for (const char of searchChars) {
        try {
          const searchUrl = `${AXMOL_SEARCH_BASE}/all_${char}.js`;
          console.log(`📥 获取: ${searchUrl}`);
          
          const response = await axios.get(searchUrl, {
            timeout: 8000,
            headers: {
              'User-Agent': 'Axmol-MCP-Test',
              'Accept': 'application/javascript, text/javascript, */*'
            }
          });
          
          const searchData = parseSearchData(response.data);
          console.log(`✅ 解析到 ${searchData.length} 个搜索项`);

          // 调试：显示前几个搜索项
          if (searchData.length > 0) {
            console.log(`📋 前3个搜索项示例:`);
            searchData.slice(0, 3).forEach((item, idx) => {
              console.log(`   ${idx + 1}. ID: ${item[0]}, Name: ${item[1][0]}`);
            });
          }

          const relevantResults = filterRelevantResults(searchData, testCase.searchTerms, testCase.categories);
          console.log(`🔍 过滤后找到 ${relevantResults.length} 个相关结果`);
          results.push(...relevantResults);
          
        } catch (error) {
          console.log(`❌ 获取搜索数据失败 (${char}):`, error.message);
        }
      }
      
      // 排序并显示结果
      const sortedResults = results
        .sort((a, b) => b.relevanceScore - a.relevanceScore)
        .slice(0, 5);
      
      console.log(`\n🎯 找到 ${sortedResults.length} 个相关结果:`);
      sortedResults.forEach((result, index) => {
        console.log(`\n${index + 1}. ${result.name}`);
        console.log(`   相关性分数: ${result.relevanceScore}`);
        console.log(`   匹配关键词: ${result.matchedTerms.join(', ')}`);
        if (result.urls && result.urls.length > 0) {
          result.urls.forEach(url => {
            const fullUrl = `${AXMOL_OFFICIAL_DOCS}/${url.path}`;
            console.log(`   📖 文档链接: ${fullUrl}`);
            if (url.scope) {
              console.log(`   📍 作用域: ${url.scope}`);
            }
          });
        }
      });
      
    } catch (error) {
      console.log(`❌ 测试失败:`, error.message);
    }
  }
  
  console.log('\n✅ 测试完成！');
}

// 运行测试
testOfficialDocsSearch().catch(console.error);
